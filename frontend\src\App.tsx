import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

// Layout components
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Pages
import HomePage from './pages/HomePage';
import CoursesPage from './pages/CoursesPage';
import CourseDetailPage from './pages/CourseDetailPage';
import CategoryPage from './pages/CategoryPage';
import LoginPage from './pages/Auth/LoginPage';
import RegisterPage from './pages/Auth/RegisterPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
import ProfilePage from './pages/Dashboard/ProfilePage';
import MyCoursesPage from './pages/Dashboard/MyCoursesPage';
import InstructorDashboard from './pages/Instructor/InstructorDashboard';
import CreateCoursePage from './pages/Instructor/CreateCoursePage';
import EditCoursePage from './pages/Instructor/EditCoursePage';
import CartPage from './pages/CartPage';
import CheckoutPage from './pages/CheckoutPage';
import NotFoundPage from './pages/NotFoundPage';

// Hooks
import { useAuthStore } from './store/authStore';

function App() {
  const { user } = useAuthStore();

  return (
    <>
      <Helmet>
        <title>EduPlatform - Nền tảng đào tạo trực tuyến</title>
        <meta name="description" content="Học online với hàng nghìn khóa học chất lượng cao từ các chuyên gia hàng đầu" />
        <meta name="keywords" content="học online, khóa học, đào tạo, giáo dục, kỹ năng" />
        <meta property="og:title" content="EduPlatform - Nền tảng đào tạo trực tuyến" />
        <meta property="og:description" content="Học online với hàng nghìn khóa học chất lượng cao từ các chuyên gia hàng đầu" />
        <meta property="og:type" content="website" />
      </Helmet>

      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route path="courses" element={<CoursesPage />} />
          <Route path="courses/:slug" element={<CourseDetailPage />} />
          <Route path="category/:slug" element={<CategoryPage />} />
          <Route path="cart" element={<CartPage />} />
          
          {/* Auth routes */}
          <Route path="login" element={<LoginPage />} />
          <Route path="register" element={<RegisterPage />} />
          
          {/* Protected routes */}
          <Route path="dashboard" element={
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          } />
          
          <Route path="profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
          
          <Route path="my-courses" element={
            <ProtectedRoute>
              <MyCoursesPage />
            </ProtectedRoute>
          } />
          
          <Route path="checkout" element={
            <ProtectedRoute>
              <CheckoutPage />
            </ProtectedRoute>
          } />
          
          {/* Instructor routes */}
          <Route path="instructor" element={
            <ProtectedRoute requiredRole="instructor">
              <InstructorDashboard />
            </ProtectedRoute>
          } />
          
          <Route path="instructor/courses/create" element={
            <ProtectedRoute requiredRole="instructor">
              <CreateCoursePage />
            </ProtectedRoute>
          } />
          
          <Route path="instructor/courses/:id/edit" element={
            <ProtectedRoute requiredRole="instructor">
              <EditCoursePage />
            </ProtectedRoute>
          } />
          
          {/* 404 page */}
          <Route path="*" element={<NotFoundPage />} />
        </Route>
      </Routes>
    </>
  );
}

export default App;
