from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.schemas.auth import UserRegister
from app.core.security import get_password_hash, verify_password


class UserService:
    def __init__(self, db: Session):
        self.db = db

    def get_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        return self.db.query(User).filter(User.id == user_id).first()

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return self.db.query(User).filter(User.email == email).first()

    def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        return self.db.query(User).filter(User.username == username).first()

    def create_user(self, user_data: UserRegister) -> User:
        """Create new user"""
        hashed_password = get_password_hash(user_data.password)
        
        db_user = User(
            email=user_data.email,
            username=user_data.username,
            full_name=user_data.full_name,
            phone=user_data.phone,
            hashed_password=hashed_password,
            role="student",
            is_active=True,
            is_verified=False
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        return db_user

    def authenticate(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        user = self.get_by_email(email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    def update_user(self, user: User, user_data: UserUpdate) -> User:
        """Update user information"""
        update_data = user_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        return user

    def update_password(self, user: User, new_password: str) -> User:
        """Update user password"""
        user.hashed_password = get_password_hash(new_password)
        self.db.commit()
        self.db.refresh(user)
        return user

    def deactivate_user(self, user: User) -> User:
        """Deactivate user account"""
        user.is_active = False
        self.db.commit()
        self.db.refresh(user)
        return user

    def activate_user(self, user: User) -> User:
        """Activate user account"""
        user.is_active = True
        self.db.commit()
        self.db.refresh(user)
        return user

    def verify_user(self, user: User) -> User:
        """Verify user email"""
        user.is_verified = True
        self.db.commit()
        self.db.refresh(user)
        return user

    def get_users(
        self, 
        skip: int = 0, 
        limit: int = 100,
        role: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[User]:
        """Get list of users with filters"""
        query = self.db.query(User)
        
        if role:
            query = query.filter(User.role == role)
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        return query.offset(skip).limit(limit).all()

    def count_users(
        self,
        role: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """Count users with filters"""
        query = self.db.query(User)
        
        if role:
            query = query.filter(User.role == role)
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        return query.count()

    def search_users(self, search_term: str, limit: int = 20) -> List[User]:
        """Search users by name or email"""
        return self.db.query(User).filter(
            and_(
                User.is_active == True,
                (
                    User.full_name.ilike(f"%{search_term}%") |
                    User.email.ilike(f"%{search_term}%") |
                    User.username.ilike(f"%{search_term}%")
                )
            )
        ).limit(limit).all()

    def get_instructors(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get list of instructors"""
        return self.db.query(User).filter(
            and_(
                User.role == "instructor",
                User.is_active == True
            )
        ).offset(skip).limit(limit).all()

    def delete_user(self, user: User) -> bool:
        """Delete user (soft delete by deactivating)"""
        user.is_active = False
        self.db.commit()
        return True
