from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.schemas.course import CategoryResponse, CategoryCreate, CategoryUpdate
from app.services.category_service import CategoryService
from app.api.deps import get_current_admin

router = APIRouter()


@router.get("/", response_model=List[CategoryResponse])
def get_categories(
    include_inactive: bool = False,
    db: Session = Depends(get_db)
) -> Any:
    """Get all categories with hierarchical structure"""
    category_service = CategoryService(db)
    categories = category_service.get_categories_tree(include_inactive=include_inactive)
    return categories


@router.get("/flat", response_model=List[CategoryResponse])
def get_categories_flat(
    include_inactive: bool = False,
    db: Session = Depends(get_db)
) -> Any:
    """Get all categories as flat list"""
    category_service = CategoryService(db)
    categories = category_service.get_categories_flat(include_inactive=include_inactive)
    return categories


@router.post("/", response_model=CategoryResponse)
def create_category(
    category_data: CategoryCreate,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Create new category (admin only)"""
    category_service = CategoryService(db)
    
    # Check if slug already exists
    if category_service.get_by_slug(category_data.slug):
        raise HTTPException(
            status_code=400,
            detail="Category with this slug already exists"
        )
    
    category = category_service.create_category(category_data)
    return category


@router.get("/{category_id}", response_model=CategoryResponse)
def get_category(
    category_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """Get category by ID"""
    category_service = CategoryService(db)
    category = category_service.get_by_id(category_id)
    
    if not category:
        raise HTTPException(
            status_code=404,
            detail="Category not found"
        )
    
    return category


@router.put("/{category_id}", response_model=CategoryResponse)
def update_category(
    category_id: int,
    category_update: CategoryUpdate,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Update category (admin only)"""
    category_service = CategoryService(db)
    category = category_service.get_by_id(category_id)
    
    if not category:
        raise HTTPException(
            status_code=404,
            detail="Category not found"
        )
    
    updated_category = category_service.update_category(category, category_update)
    return updated_category


@router.delete("/{category_id}")
def delete_category(
    category_id: int,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Delete category (admin only)"""
    category_service = CategoryService(db)
    category = category_service.get_by_id(category_id)
    
    if not category:
        raise HTTPException(
            status_code=404,
            detail="Category not found"
        )
    
    # Check if category has courses
    if category.courses:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete category with courses"
        )
    
    category_service.delete_category(category)
    return {"message": "Category deleted successfully"}
