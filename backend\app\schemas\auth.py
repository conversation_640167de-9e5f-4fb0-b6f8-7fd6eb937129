from pydantic import BaseModel, EmailStr
from typing import Optional


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    email: Optional[str] = None


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class UserRegister(BaseModel):
    email: EmailStr
    password: str
    full_name: str
    username: Optional[str] = None
    phone: Optional[str] = None


class PasswordReset(BaseModel):
    token: str
    new_password: str


class ForgotPassword(BaseModel):
    email: EmailStr
