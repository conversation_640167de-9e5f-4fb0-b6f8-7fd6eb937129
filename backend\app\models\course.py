from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, DECIMAL, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class Category(Base):
    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    slug = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey("categories.id"))
    image_url = Column(String(500))
    icon = Column(String(100))
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Self-referential relationship for hierarchical categories
    parent = relationship("Category", remote_side=[id], back_populates="children")
    children = relationship("Category", back_populates="parent")
    
    # Courses in this category
    courses = relationship("Course", back_populates="category")

    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}', slug='{self.slug}')>"


class Course(Base):
    __tablename__ = "courses"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)
    title = Column(String(500), nullable=False)
    slug = Column(String(500), unique=True, nullable=False, index=True)
    description = Column(Text)
    short_description = Column(String(1000))
    thumbnail_url = Column(String(500))
    preview_video_url = Column(String(500))
    price = Column(DECIMAL(10, 2), default=0)
    original_price = Column(DECIMAL(10, 2))
    currency = Column(String(3), default="VND")
    level = Column(String(20), default="beginner")  # beginner, intermediate, advanced
    language = Column(String(10), default="vi")
    duration_hours = Column(Integer, default=0)
    total_lessons = Column(Integer, default=0)
    instructor_id = Column(Integer, ForeignKey("users.id"))
    category_id = Column(Integer, ForeignKey("categories.id"))
    status = Column(String(20), default="draft")  # draft, published, archived
    is_featured = Column(Boolean, default=False)
    is_bestseller = Column(Boolean, default=False)
    requirements = Column(Text)
    what_you_learn = Column(Text)
    target_audience = Column(Text)
    certificate_available = Column(Boolean, default=True)
    avg_rating = Column(DECIMAL(3, 2), default=0)
    total_reviews = Column(Integer, default=0)
    total_students = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    published_at = Column(DateTime(timezone=True))

    # Relationships
    instructor = relationship("User", back_populates="courses_taught")
    category = relationship("Category", back_populates="courses")
    sections = relationship("CourseSection", back_populates="course", cascade="all, delete-orphan")
    lessons = relationship("Lesson", back_populates="course", cascade="all, delete-orphan")
    enrollments = relationship("Enrollment", back_populates="course")
    reviews = relationship("Review", back_populates="course")
    order_items = relationship("OrderItem", back_populates="course")
    wishlists = relationship("Wishlist", back_populates="course")
    cart_items = relationship("CartItem", back_populates="course")

    def __repr__(self):
        return f"<Course(id={self.id}, title='{self.title}', status='{self.status}')>"


class CourseSection(Base):
    __tablename__ = "course_sections"

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(Integer, ForeignKey("courses.id"))
    title = Column(String(255), nullable=False)
    description = Column(Text)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="sections")
    lessons = relationship("Lesson", back_populates="section", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<CourseSection(id={self.id}, title='{self.title}')>"


class Lesson(Base):
    __tablename__ = "lessons"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)
    course_id = Column(Integer, ForeignKey("courses.id"))
    section_id = Column(Integer, ForeignKey("course_sections.id"))
    title = Column(String(500), nullable=False)
    slug = Column(String(500), nullable=False)
    description = Column(Text)
    content = Column(Text)
    video_url = Column(String(500))
    video_duration = Column(Integer, default=0)  # in seconds
    lesson_type = Column(String(20), default="video")  # video, text, quiz, assignment
    sort_order = Column(Integer, default=0)
    is_preview = Column(Boolean, default=False)
    is_published = Column(Boolean, default=True)
    resources = Column(JSONB)  # Additional resources, files, links
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="lessons")
    section = relationship("CourseSection", back_populates="lessons")
    lesson_progress = relationship("LessonProgress", back_populates="lesson")

    def __repr__(self):
        return f"<Lesson(id={self.id}, title='{self.title}', type='{self.lesson_type}')>"
