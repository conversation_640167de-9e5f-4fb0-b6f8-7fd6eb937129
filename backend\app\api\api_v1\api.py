from fastapi import APIRouter
from app.api.api_v1.endpoints import (
    auth,
    users,
    courses,
    categories,
    lessons,
    orders,
    reviews,
    instructors,
    dashboard
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(courses.router, prefix="/courses", tags=["courses"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(lessons.router, prefix="/lessons", tags=["lessons"])
api_router.include_router(orders.router, prefix="/orders", tags=["orders"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
api_router.include_router(instructors.router, prefix="/instructors", tags=["instructors"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
