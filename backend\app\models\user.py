from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime, Date, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True)
    full_name = Column(String(255))
    hashed_password = Column(String(255), nullable=False)
    phone = Column(String(20))
    avatar_url = Column(String(500))
    bio = Column(Text)
    role = Column(String(20), default="student")  # student, instructor, admin
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    date_of_birth = Column(Date)
    gender = Column(String(10))  # male, female, other
    address = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    courses_taught = relationship("Course", back_populates="instructor")
    enrollments = relationship("Enrollment", back_populates="user")
    reviews = relationship("Review", back_populates="user")
    orders = relationship("Order", back_populates="user")
    wishlists = relationship("Wishlist", back_populates="user")
    cart_items = relationship("CartItem", back_populates="user")
    notifications = relationship("Notification", back_populates="user")
    lesson_progress = relationship("LessonProgress", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', role='{self.role}')>"
