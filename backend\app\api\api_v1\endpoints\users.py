from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.schemas.user import UserResponse, UserUpdate, UserList, PasswordChange
from app.services.user_service import UserService
from app.api.deps import get_current_user, get_current_admin
from app.core.security import verify_password, get_password_hash

router = APIRouter()


@router.get("/me", response_model=UserResponse)
def get_current_user_profile(
    current_user: User = Depends(get_current_user)
) -> Any:
    """Get current user profile"""
    return current_user


@router.put("/me", response_model=UserResponse)
def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Update current user profile"""
    user_service = UserService(db)
    updated_user = user_service.update_user(current_user, user_update)
    return updated_user


@router.post("/me/change-password")
def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Change current user password"""
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="Incorrect current password"
        )
    
    # Update password
    user_service = UserService(db)
    user_service.update_password(current_user, password_data.new_password)
    
    return {"message": "Password updated successfully"}


@router.get("/", response_model=UserList)
def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    role: str = Query(None),
    is_active: bool = Query(None),
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Get list of users (admin only)"""
    user_service = UserService(db)
    
    users = user_service.get_users(
        skip=skip,
        limit=limit,
        role=role,
        is_active=is_active
    )
    
    total = user_service.count_users(role=role, is_active=is_active)
    pages = (total + limit - 1) // limit
    
    return UserList(
        users=users,
        total=total,
        page=(skip // limit) + 1,
        size=limit,
        pages=pages
    )


@router.get("/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Get user by ID (admin only)"""
    user_service = UserService(db)
    user = user_service.get_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )
    
    return user


@router.put("/{user_id}", response_model=UserResponse)
def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Update user (admin only)"""
    user_service = UserService(db)
    user = user_service.get_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )
    
    updated_user = user_service.update_user(user, user_update)
    return updated_user


@router.post("/{user_id}/deactivate")
def deactivate_user(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Deactivate user (admin only)"""
    user_service = UserService(db)
    user = user_service.get_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )
    
    if user.id == current_user.id:
        raise HTTPException(
            status_code=400,
            detail="Cannot deactivate your own account"
        )
    
    user_service.deactivate_user(user)
    return {"message": "User deactivated successfully"}


@router.post("/{user_id}/activate")
def activate_user(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Any:
    """Activate user (admin only)"""
    user_service = UserService(db)
    user = user_service.get_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )
    
    user_service.activate_user(user)
    return {"message": "User activated successfully"}


@router.get("/search/", response_model=List[UserResponse])
def search_users(
    q: str = Query(..., min_length=2),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Search users by name or email"""
    user_service = UserService(db)
    users = user_service.search_users(q)
    return users


@router.get("/instructors/", response_model=List[UserResponse])
def get_instructors(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db)
) -> Any:
    """Get list of instructors"""
    user_service = UserService(db)
    instructors = user_service.get_instructors(skip=skip, limit=limit)
    return instructors
