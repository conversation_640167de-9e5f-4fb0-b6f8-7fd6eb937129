-- Seed data for EduPlatform

-- Insert sample categories
INSERT INTO categories (name, slug, description, parent_id, icon, sort_order) VALUES
('Kinh Doanh', 'kinh-doanh', '<PERSON><PERSON><PERSON> khóa học về kinh doanh và quản trị', NULL, 'business', 1),
('Ngoại Ngữ', 'ngoai-ngu', '<PERSON>ọc ngoại ngữ từ cơ bản đến nâng cao', NULL, 'language', 2),
('Thiết Kế', 'thiet-ke', 'Thiết kế đồ họa, web, UI/UX', NULL, 'design', 3),
('<PERSON><PERSON> Năng', 'ky-nang', 'Phát triển kỹ năng mềm', NULL, 'skills', 4),
('Lập Trình & CNTT', 'lap-trinh-cntt', 'Lập trình và công nghệ thông tin', NULL, 'code', 5),
('Marketing', 'marketing', 'Digital Marketing và quảng cáo', NULL, 'marketing', 6),
('Tin Học Văn Phòng', 'tin-hoc-van-phong', 'Excel, Word, PowerPoint', NULL, 'office', 7),
('Âm Nhạc', 'am-nhac', 'Học nhạc cụ và thanh nhạc', NULL, 'music', 8);

-- Insert subcategories
INSERT INTO categories (name, slug, description, parent_id, sort_order) VALUES
('Quản Trị Kinh Doanh', 'quan-tri-kinh-doanh', 'Quản lý và điều hành doanh nghiệp', 1, 1),
('Kinh Doanh Online', 'kinh-doanh-online', 'Kinh doanh trên nền tảng số', 1, 2),
('Tiếng Anh', 'tieng-anh', 'Học tiếng Anh giao tiếp và chuyên ngành', 2, 1),
('Tiếng Nhật', 'tieng-nhat', 'Học tiếng Nhật từ N5 đến N1', 2, 2),
('Thiết Kế Đồ Họa', 'thiet-ke-do-hoa', 'Photoshop, Illustrator, CorelDraw', 3, 1),
('Thiết Kế Web', 'thiet-ke-web', 'HTML, CSS, JavaScript, UI/UX', 3, 2),
('Kỹ Năng Giao Tiếp', 'ky-nang-giao-tiep', 'Giao tiếp hiệu quả trong công việc', 4, 1),
('Kỹ Năng Lãnh Đạo', 'ky-nang-lanh-dao', 'Phát triển khả năng lãnh đạo', 4, 2),
('Lập Trình Web', 'lap-trinh-web', 'Frontend và Backend development', 5, 1),
('Lập Trình Mobile', 'lap-trinh-mobile', 'Android, iOS, React Native', 5, 2),
('Digital Marketing', 'digital-marketing', 'Facebook Ads, Google Ads, SEO', 6, 1),
('Content Marketing', 'content-marketing', 'Viết content và storytelling', 6, 2),
('Microsoft Office', 'microsoft-office', 'Word, Excel, PowerPoint chuyên sâu', 7, 1),
('Google Workspace', 'google-workspace', 'Google Docs, Sheets, Slides', 7, 2),
('Guitar', 'guitar', 'Học đàn guitar cơ bản và nâng cao', 8, 1),
('Piano', 'piano', 'Học đàn piano từ cơ bản', 8, 2);

-- Insert sample admin user
INSERT INTO users (email, username, full_name, hashed_password, role, is_active, is_verified) VALUES
('<EMAIL>', 'admin', 'Administrator', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Zem', 'admin', true, true);

-- Insert sample instructor
INSERT INTO users (email, username, full_name, hashed_password, role, is_active, is_verified, bio) VALUES
('<EMAIL>', 'instructor1', 'Nguyễn Văn Giảng', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Zem', 'instructor', true, true, 'Giảng viên có 10 năm kinh nghiệm trong lĩnh vực lập trình web');

-- Insert sample student
INSERT INTO users (email, username, full_name, hashed_password, role, is_active, is_verified) VALUES
('<EMAIL>', 'student1', 'Trần Thị Học', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Zem', 'student', true, true);

-- Insert sample courses
INSERT INTO courses (title, slug, description, short_description, price, original_price, level, instructor_id, category_id, status, is_featured, requirements, what_you_learn, target_audience) VALUES
(
    'Lập trình Web với React và Node.js',
    'lap-trinh-web-react-nodejs',
    'Khóa học toàn diện về phát triển ứng dụng web hiện đại với React frontend và Node.js backend. Học viên sẽ được hướng dẫn từ cơ bản đến nâng cao, xây dựng các dự án thực tế.',
    'Học React và Node.js từ cơ bản đến nâng cao với các dự án thực tế',
    1999000,
    2999000,
    'intermediate',
    2,
    9,
    'published',
    true,
    'Kiến thức cơ bản về HTML, CSS, JavaScript',
    'Xây dựng ứng dụng web full-stack, API RESTful, Authentication, Database integration',
    'Lập trình viên muốn học công nghệ web hiện đại'
),
(
    'Thiết kế UI/UX với Figma',
    'thiet-ke-ui-ux-figma',
    'Khóa học thiết kế giao diện người dùng và trải nghiệm người dùng với công cụ Figma. Từ wireframe đến prototype hoàn chỉnh.',
    'Thiết kế UI/UX chuyên nghiệp với Figma từ A-Z',
    1499000,
    1999000,
    'beginner',
    2,
    12,
    'published',
    false,
    'Không yêu cầu kinh nghiệm trước đó',
    'Thiết kế wireframe, mockup, prototype, design system',
    'Designer, Developer muốn học UI/UX'
),
(
    'Digital Marketing toàn diện',
    'digital-marketing-toan-dien',
    'Khóa học Digital Marketing từ cơ bản đến nâng cao, bao gồm Facebook Ads, Google Ads, SEO, Content Marketing và Analytics.',
    'Làm chủ Digital Marketing với Facebook Ads, Google Ads, SEO',
    2499000,
    3499000,
    'intermediate',
    2,
    17,
    'published',
    true,
    'Kiến thức cơ bản về marketing',
    'Chạy quảng cáo hiệu quả, SEO website, Content strategy, Analytics',
    'Marketer, Chủ doanh nghiệp, Freelancer'
);

-- Insert course sections
INSERT INTO course_sections (course_id, title, description, sort_order) VALUES
(1, 'Giới thiệu và cài đặt môi trường', 'Tổng quan về React và Node.js, cài đặt các công cụ cần thiết', 1),
(1, 'React Fundamentals', 'Học các khái niệm cơ bản của React', 2),
(1, 'Node.js và Express', 'Xây dựng API với Node.js và Express', 3),
(1, 'Database và Authentication', 'Kết nối database và xác thực người dùng', 4),
(1, 'Dự án thực tế', 'Xây dựng ứng dụng hoàn chỉnh', 5);

-- Insert sample lessons
INSERT INTO lessons (course_id, section_id, title, slug, description, lesson_type, sort_order, is_preview, video_duration) VALUES
(1, 1, 'Giới thiệu khóa học', 'gioi-thieu-khoa-hoc', 'Tổng quan về nội dung khóa học và lộ trình học tập', 'video', 1, true, 600),
(1, 1, 'Cài đặt Node.js và npm', 'cai-dat-nodejs-npm', 'Hướng dẫn cài đặt Node.js và npm trên các hệ điều hành', 'video', 2, true, 900),
(1, 1, 'Cài đặt Visual Studio Code', 'cai-dat-vscode', 'Cài đặt và cấu hình VS Code cho phát triển web', 'video', 3, false, 720),
(1, 2, 'JSX và Components', 'jsx-va-components', 'Hiểu về JSX và cách tạo components trong React', 'video', 1, false, 1200),
(1, 2, 'Props và State', 'props-va-state', 'Quản lý dữ liệu với Props và State', 'video', 2, false, 1500),
(1, 2, 'Event Handling', 'event-handling', 'Xử lý sự kiện trong React', 'video', 3, false, 1080);

-- Insert sample coupons
INSERT INTO coupons (code, name, description, discount_type, discount_value, minimum_amount, usage_limit, is_active, valid_from, valid_until) VALUES
('WELCOME20', 'Chào mừng thành viên mới', 'Giảm 20% cho đơn hàng đầu tiên', 'percentage', 20, 500000, 1000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
('SAVE100K', 'Giảm 100K', 'Giảm 100,000đ cho đơn hàng từ 1 triệu', 'fixed', 100000, 1000000, 500, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '15 days');

-- Update course statistics
UPDATE courses SET 
    total_lessons = (SELECT COUNT(*) FROM lessons WHERE course_id = courses.id),
    duration_hours = (SELECT COALESCE(SUM(video_duration), 0) / 3600 FROM lessons WHERE course_id = courses.id),
    avg_rating = 4.5,
    total_reviews = 150,
    total_students = 1250
WHERE id IN (1, 2, 3);
