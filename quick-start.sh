#!/bin/bash

# EduPlatform Quick Start Script
echo "🚀 Starting EduPlatform setup..."

# Create project directory
mkdir -p eduplatform
cd eduplatform

# Create environment file
cat > .env << 'EOF'
# Database Configuration
DATABASE_URL=***********************************************/eduplatform
POSTGRES_DB=eduplatform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration
SECRET_KEY=your-super-secret-key-here-change-in-production-min-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Environment
ENVIRONMENT=development
DEBUG=true

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
EOF

# Create simplified docker-compose for quick start
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: eduplatform_postgres
    environment:
      POSTGRES_DB: eduplatform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - eduplatform_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: eduplatform_redis
    ports:
      - "6379:6379"
    networks:
      - eduplatform_network

  # FastAPI Backend
  backend:
    image: python:3.11-slim
    container_name: eduplatform_backend
    working_dir: /app
    environment:
      - DATABASE_URL=***********************************************/eduplatform
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-here
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - eduplatform_network
    command: >
      bash -c "
        pip install fastapi uvicorn sqlalchemy psycopg2-binary python-jose[cryptography] passlib[bcrypt] python-multipart &&
        python -c \"
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title='EduPlatform API', version='1.0.0')

app.add_middleware(
    CORSMiddleware,
    allow_origins=['http://localhost:3000'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

@app.get('/')
def root():
    return {'message': 'EduPlatform API is running!', 'status': 'success'}

@app.get('/health')
def health():
    return {'status': 'healthy'}

@app.get('/api/v1/courses')
def get_courses():
    return {
        'courses': [
            {
                'id': 1,
                'title': 'Lập trình Web với React và Node.js',
                'price': 1999000,
                'instructor': 'Nguyễn Văn A',
                'rating': 4.5,
                'students': 1250
            },
            {
                'id': 2,
                'title': 'Thiết kế UI/UX với Figma',
                'price': 1499000,
                'instructor': 'Trần Thị B',
                'rating': 4.7,
                'students': 890
            }
        ],
        'total': 2
    }

if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, host='0.0.0.0', port=8000)
\" > main.py &&
        python main.py
      "

  # React Frontend
  frontend:
    image: node:18-alpine
    container_name: eduplatform_frontend
    working_dir: /app
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
    networks:
      - eduplatform_network
    command: >
      sh -c "
        if [ ! -f package.json ]; then
          npx create-react-app . --template typescript &&
          npm install axios react-router-dom @types/react-router-dom
        fi &&
        echo \"
import React from 'react';
import './App.css';

function App() {
  const [courses, setCourses] = React.useState([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    fetch('http://localhost:8000/api/v1/courses')
      .then(res => res.json())
      .then(data => {
        setCourses(data.courses || []);
        setLoading(false);
      })
      .catch(err => {
        console.error(err);
        setLoading(false);
      });
  }, []);

  return (
    <div className='App'>
      <header className='App-header' style={{padding: '20px', background: '#f8f9fa'}}>
        <h1 style={{color: '#333', margin: 0}}>🎓 EduPlatform</h1>
        <p style={{color: '#666', margin: '10px 0'}}>Nền tảng đào tạo trực tuyến</p>
      </header>
      
      <main style={{padding: '40px 20px', maxWidth: '1200px', margin: '0 auto'}}>
        <h2 style={{color: '#333', marginBottom: '30px'}}>Khóa học nổi bật</h2>
        
        {loading ? (
          <div style={{textAlign: 'center', padding: '40px'}}>
            <div style={{
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #3498db',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              animation: 'spin 1s linear infinite',
              margin: '0 auto'
            }}></div>
            <p style={{marginTop: '20px', color: '#666'}}>Đang tải khóa học...</p>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '20px'
          }}>
            {courses.map((course: any) => (
              <div key={course.id} style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                padding: '20px',
                backgroundColor: 'white',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
              >
                <h3 style={{color: '#333', marginBottom: '10px'}}>{course.title}</h3>
                <p style={{color: '#666', marginBottom: '10px'}}>Giảng viên: {course.instructor}</p>
                <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                  <span style={{color: '#e74c3c', fontWeight: 'bold', fontSize: '18px'}}>
                    {course.price.toLocaleString('vi-VN')}đ
                  </span>
                  <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
                    <span style={{color: '#f39c12'}}>⭐ {course.rating}</span>
                    <span style={{color: '#666'}}>({course.students} học viên)</span>
                  </div>
                </div>
                <button style={{
                  width: '100%',
                  padding: '10px',
                  marginTop: '15px',
                  backgroundColor: '#3498db',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2980b9'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3498db'}
                >
                  Xem chi tiết
                </button>
              </div>
            ))}
          </div>
        )}
        
        <div style={{
          marginTop: '60px',
          padding: '40px',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <h3 style={{color: '#333', marginBottom: '20px'}}>🎯 Tính năng chính</h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px',
            marginTop: '20px'
          }}>
            <div>
              <h4 style={{color: '#3498db'}}>🎓 Khóa học đa dạng</h4>
              <p style={{color: '#666', fontSize: '14px'}}>Hàng nghìn khóa học chất lượng cao</p>
            </div>
            <div>
              <h4 style={{color: '#e74c3c'}}>👨‍🏫 Giảng viên chuyên nghiệp</h4>
              <p style={{color: '#666', fontSize: '14px'}}>Học từ các chuyên gia hàng đầu</p>
            </div>
            <div>
              <h4 style={{color: '#27ae60'}}>📱 Học mọi lúc mọi nơi</h4>
              <p style={{color: '#666', fontSize: '14px'}}>Truy cập trên mọi thiết bị</p>
            </div>
            <div>
              <h4 style={{color: '#f39c12'}}>🏆 Chứng chỉ hoàn thành</h4>
              <p style={{color: '#666', fontSize: '14px'}}>Nhận chứng chỉ sau khi hoàn thành</p>
            </div>
          </div>
        </div>
      </main>
      
      <footer style={{
        backgroundColor: '#2c3e50',
        color: 'white',
        padding: '40px 20px',
        textAlign: 'center',
        marginTop: '60px'
      }}>
        <p>&copy; 2024 EduPlatform. Nền tảng đào tạo trực tuyến hàng đầu Việt Nam.</p>
        <p style={{marginTop: '10px', opacity: 0.8}}>
          API Status: <span style={{color: '#27ae60'}}>✅ Connected</span>
        </p>
      </footer>
      
      <style>{\`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      \`}</style>
    </div>
  );
}

export default App;
\" > src/App.tsx &&
        npm start
      "

volumes:
  postgres_data:

networks:
  eduplatform_network:
    driver: bridge
EOF

# Create basic database schema
cat > init.sql << 'EOF'
-- Basic database schema for quick start
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    role VARCHAR(20) DEFAULT 'student',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS courses (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    price DECIMAL(10,2) DEFAULT 0,
    instructor_name VARCHAR(255),
    rating DECIMAL(3,2) DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO courses (title, price, instructor_name, rating, total_students) VALUES
('Lập trình Web với React và Node.js', 1999000, 'Nguyễn Văn A', 4.5, 1250),
('Thiết kế UI/UX với Figma', 1499000, 'Trần Thị B', 4.7, 890),
('Digital Marketing toàn diện', 2499000, 'Lê Văn C', 4.6, 756),
('Python cho Data Science', 1799000, 'Phạm Thị D', 4.8, 1100);
EOF

echo "✅ Setup completed!"
echo ""
echo "🚀 To start the application:"
echo "   docker-compose up -d"
echo ""
echo "📱 Access the application:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "🛑 To stop the application:"
echo "   docker-compose down"
echo ""
echo "📊 To view logs:"
echo "   docker-compose logs -f"
