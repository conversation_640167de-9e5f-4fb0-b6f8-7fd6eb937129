from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.api.deps import get_current_user

router = APIRouter()


@router.get("/")
def get_instructors(
    db: Session = Depends(get_db)
) -> Any:
    """Get instructors"""
    # TODO: Implement instructor service
    return {"message": "Instructors endpoint - to be implemented"}


@router.get("/{instructor_id}")
def get_instructor(
    instructor_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """Get instructor details"""
    # TODO: Implement instructor details
    return {"message": "Instructor details endpoint - to be implemented"}
