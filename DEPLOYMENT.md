# Hướng dẫn triển khai EduPlatform

## Y<PERSON><PERSON> c<PERSON>u hệ thống

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+

### Recommended Requirements
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **OS**: Ubuntu 22.04 LTS

## Cài đặt Dependencies

### 1. Docker & Docker Compose
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Node.js (nếu chạy development)
```bash
# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 3. Python (nếu chạy development)
```bash
# Install Python 3.11
sudo apt update
sudo apt install python3.11 python3.11-venv python3-pip
```

## Triển khai Production

### 1. Clone Repository
```bash
git clone <repository-url>
cd eduplatform
```

### 2. Cấu hình Environment
```bash
# Copy và chỉnh sửa file environment
cp .env.example .env

# Chỉnh sửa các biến môi trường quan trọng:
nano .env
```

**Các biến quan trọng cần thay đổi:**
```env
# Database
DATABASE_URL=***************************************************/eduplatform
POSTGRES_PASSWORD=STRONG_PASSWORD

# Security
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ENVIRONMENT=production
DEBUG=false

# Email
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# AWS S3 (cho video storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=eduplatform-videos

# Payment Gateway
VNPAY_TMN_CODE=your-vnpay-code
VNPAY_HASH_SECRET=your-vnpay-secret
```

### 3. Cấu hình SSL/HTTPS
```bash
# Tạo thư mục cho SSL certificates
mkdir -p nginx/ssl

# Sử dụng Let's Encrypt (khuyến nghị)
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/
```

### 4. Cấu hình Nginx cho Production
```bash
# Tạo file nginx.conf cho production
cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Backend API
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files
        location /static/ {
            proxy_pass http://backend;
        }
    }
}
EOF
```

### 5. Chạy Production
```bash
# Build và chạy containers
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Kiểm tra logs
docker-compose logs -f

# Kiểm tra status
docker-compose ps
```

### 6. Database Migration & Seed Data
```bash
# Chạy database migrations
docker-compose exec backend alembic upgrade head

# Load seed data (optional)
docker-compose exec postgres psql -U postgres -d eduplatform -f /docker-entrypoint-initdb.d/seed.sql
```

## Monitoring & Maintenance

### 1. Health Checks
```bash
# Kiểm tra health của services
curl http://localhost:8000/health
curl http://localhost:3000

# Kiểm tra database
docker-compose exec postgres pg_isready -U postgres
```

### 2. Backup Database
```bash
# Tạo backup
docker-compose exec postgres pg_dump -U postgres eduplatform > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
docker-compose exec -T postgres psql -U postgres eduplatform < backup_file.sql
```

### 3. Log Management
```bash
# Xem logs realtime
docker-compose logs -f backend
docker-compose logs -f frontend

# Rotate logs (thêm vào crontab)
0 2 * * * docker system prune -f
```

### 4. SSL Certificate Renewal
```bash
# Thêm vào crontab để auto-renew
0 3 * * 1 certbot renew --quiet && docker-compose restart nginx
```

## Performance Optimization

### 1. Database Optimization
```sql
-- Tạo indexes cho performance
CREATE INDEX CONCURRENTLY idx_courses_published ON courses(status, published_at) WHERE status = 'published';
CREATE INDEX CONCURRENTLY idx_enrollments_user_course ON enrollments(user_id, course_id);
CREATE INDEX CONCURRENTLY idx_lesson_progress_user ON lesson_progress(user_id, is_completed);
```

### 2. Redis Caching
```bash
# Cấu hình Redis cho caching
docker-compose exec redis redis-cli CONFIG SET maxmemory 256mb
docker-compose exec redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### 3. CDN Setup (Optional)
- Sử dụng CloudFlare hoặc AWS CloudFront
- Cấu hình cache cho static files
- Optimize images và videos

## Security Checklist

- [ ] Thay đổi tất cả default passwords
- [ ] Cấu hình firewall (UFW/iptables)
- [ ] Enable SSL/HTTPS
- [ ] Cấu hình rate limiting
- [ ] Regular security updates
- [ ] Backup strategy
- [ ] Monitor logs for suspicious activity

## Troubleshooting

### Common Issues

1. **Database connection failed**
   ```bash
   # Kiểm tra database container
   docker-compose logs postgres
   docker-compose exec postgres pg_isready
   ```

2. **Frontend không load**
   ```bash
   # Rebuild frontend
   docker-compose build frontend
   docker-compose up -d frontend
   ```

3. **API 500 errors**
   ```bash
   # Kiểm tra backend logs
   docker-compose logs backend
   ```

4. **Out of disk space**
   ```bash
   # Clean up Docker
   docker system prune -a
   docker volume prune
   ```

## Support

Nếu gặp vấn đề trong quá trình triển khai, vui lòng:
1. Kiểm tra logs của các services
2. Xem lại cấu hình environment variables
3. Đảm bảo ports không bị conflict
4. Kiểm tra firewall settings
