from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.api.deps import get_current_user

router = APIRouter()


@router.get("/")
def get_reviews(
    db: Session = Depends(get_db)
) -> Any:
    """Get reviews"""
    # TODO: Implement review service
    return {"message": "Reviews endpoint - to be implemented"}


@router.post("/")
def create_review(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Create new review"""
    # TODO: Implement review creation
    return {"message": "Create review endpoint - to be implemented"}
