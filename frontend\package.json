{"name": "eduplatform-frontend", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "react-router-dom": "^6.10.0", "axios": "^1.3.4", "react-query": "^3.39.3", "zustand": "^4.3.7", "react-hook-form": "^7.43.9", "@hookform/resolvers": "^3.0.1", "yup": "^1.1.1", "tailwindcss": "^3.3.1", "@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.17", "react-hot-toast": "^2.4.0", "react-player": "^2.12.0", "swiper": "^9.2.4", "framer-motion": "^10.12.4", "date-fns": "^2.29.3", "clsx": "^1.2.1", "react-helmet-async": "^1.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.23"}, "proxy": "http://localhost:8000"}