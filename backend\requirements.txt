# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8

# Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Background Tasks
celery==5.3.4
redis==5.0.1

# File Upload & Storage
boto3==1.34.0
Pillow==10.1.0

# Email
fastapi-mail==1.4.1

# CORS
fastapi-cors==0.0.6

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Monitoring & Logging
structlog==23.2.0

# Date & Time
python-dateutil==2.8.2

# Utilities
python-slugify==8.0.1
qrcode==7.4.2
