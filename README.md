# EduPlatform - Nền tảng đào tạo trực tuyến

Dự án xây dựng nền tảng đào tạo trực tuyến tương tự Unica.vn với FastAPI, React/Node.js và PostgreSQL.

## Kiến trúc hệ thống

```
Frontend (React/Node.js) ↔ Backend (FastAPI) ↔ Database (PostgreSQL)
                                ↓
                        External Services:
                        - Payment Gateway
                        - Video Storage (AWS S3)
                        - Email Service
                        - Push Notifications
```

## Tính năng chính

- 🎓 Hệ thống khóa học trực tuyến với video, quiz, bài tập
- 👥 Quản lý học viên và giảng viên
- 📚 Danh mục khóa học phân cấp
- 💳 Hệ thống thanh toán và giỏ hàng
- 📊 Dashboard theo dõi tiến độ học tập
- ⭐ Đ<PERSON>h gi<PERSON> và review khóa học
- 🎥 Live streaming cho buổi học trực tiếp
- 📱 Responsive design cho mobile
- 🏆 H<PERSON> thống chứng chỉ
- 💰 Affiliate marketing

## Cấu trúc dự án

```
eduplatform/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core settings, security
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
│   ├── alembic/            # Database migrations
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── frontend/               # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   ├── store/          # State management
│   │   └── utils/          # Utilities
│   ├── package.json
│   └── package-lock.json
├── database/               # Database scripts
│   ├── init.sql
│   └── seed.sql
├── docker-compose.yml      # Docker configuration
└── README.md
```

## Công nghệ sử dụng

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - ORM
- **Alembic** - Database migrations
- **Pydantic** - Data validation
- **JWT** - Authentication
- **Celery** - Background tasks
- **Redis** - Caching

### Frontend
- **React** - UI framework
- **TypeScript** - Type safety
- **Next.js** - React framework
- **Tailwind CSS** - Styling
- **React Query** - Data fetching
- **Zustand** - State management
- **React Hook Form** - Form handling

### Database
- **PostgreSQL** - Primary database
- **Redis** - Caching and sessions

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Local development
- **GitHub Actions** - CI/CD

## Cài đặt và chạy dự án

### Yêu cầu hệ thống
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose

### Chạy với Docker Compose (Khuyến nghị)

```bash
# Clone repository
git clone <repository-url>
cd eduplatform

# Chạy toàn bộ hệ thống
docker-compose up -d

# Xem logs
docker-compose logs -f
```

### Chạy thủ công

#### Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend
```bash
cd frontend
npm install
npm run dev
```

## API Documentation

Sau khi chạy backend, truy cập:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Database Schema

Xem file `database/init.sql` để biết chi tiết về cấu trúc database.

## Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## License

MIT License - xem file LICENSE để biết chi tiết.
