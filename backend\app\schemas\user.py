from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime, date
from uuid import UUID


class UserBase(BaseModel):
    email: EmailStr
    username: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    address: Optional[str] = None


class UserCreate(UserBase):
    password: str
    role: Optional[str] = "student"


class UserUpdate(BaseModel):
    username: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    address: Optional[str] = None


class UserResponse(UserBase):
    id: int
    uuid: UUID
    role: str
    is_active: bool
    is_verified: bool
    avatar_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserProfile(UserResponse):
    total_courses: Optional[int] = 0
    completed_courses: Optional[int] = 0
    certificates_earned: Optional[int] = 0


class UserList(BaseModel):
    users: List[UserResponse]
    total: int
    page: int
    size: int
    pages: int


class PasswordChange(BaseModel):
    current_password: str
    new_password: str
