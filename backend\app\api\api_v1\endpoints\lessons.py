from typing import Any
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.schemas.course import LessonResponse, LessonCreate, LessonUpdate
from app.services.lesson_service import LessonService
from app.api.deps import get_current_user, get_current_instructor

router = APIRouter()


@router.post("/", response_model=LessonResponse)
def create_lesson(
    lesson_data: LessonCreate,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Create new lesson (instructor only)"""
    lesson_service = LessonService(db)
    
    # Check if user owns the course
    course = lesson_service.get_course(lesson_data.course_id)
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    if current_user.role != "admin" and current_user.id != course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    lesson = lesson_service.create_lesson(lesson_data)
    return lesson


@router.get("/{lesson_id}", response_model=LessonResponse)
def get_lesson(
    lesson_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get lesson details"""
    lesson_service = LessonService(db)
    lesson = lesson_service.get_by_id(lesson_id)
    
    if not lesson:
        raise HTTPException(
            status_code=404,
            detail="Lesson not found"
        )
    
    # Check if user has access to this lesson
    has_access = lesson_service.check_user_access(lesson, current_user)
    
    if not has_access:
        raise HTTPException(
            status_code=403,
            detail="Access denied"
        )
    
    return lesson


@router.put("/{lesson_id}", response_model=LessonResponse)
def update_lesson(
    lesson_id: int,
    lesson_update: LessonUpdate,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Update lesson (instructor only)"""
    lesson_service = LessonService(db)
    lesson = lesson_service.get_by_id(lesson_id)
    
    if not lesson:
        raise HTTPException(
            status_code=404,
            detail="Lesson not found"
        )
    
    # Check permissions
    if current_user.role != "admin" and current_user.id != lesson.course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    updated_lesson = lesson_service.update_lesson(lesson, lesson_update)
    return updated_lesson


@router.delete("/{lesson_id}")
def delete_lesson(
    lesson_id: int,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Delete lesson (instructor only)"""
    lesson_service = LessonService(db)
    lesson = lesson_service.get_by_id(lesson_id)
    
    if not lesson:
        raise HTTPException(
            status_code=404,
            detail="Lesson not found"
        )
    
    # Check permissions
    if current_user.role != "admin" and current_user.id != lesson.course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    lesson_service.delete_lesson(lesson)
    return {"message": "Lesson deleted successfully"}


@router.post("/{lesson_id}/complete")
def mark_lesson_complete(
    lesson_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Mark lesson as completed"""
    lesson_service = LessonService(db)
    lesson = lesson_service.get_by_id(lesson_id)
    
    if not lesson:
        raise HTTPException(
            status_code=404,
            detail="Lesson not found"
        )
    
    # Check if user is enrolled in the course
    if not lesson_service.is_user_enrolled(lesson.course_id, current_user.id):
        raise HTTPException(
            status_code=403,
            detail="You are not enrolled in this course"
        )
    
    lesson_service.mark_lesson_complete(lesson_id, current_user.id)
    return {"message": "Lesson marked as completed"}


@router.post("/{lesson_id}/progress")
def update_lesson_progress(
    lesson_id: int,
    watch_time: int,
    last_position: int = 0,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Update lesson progress"""
    lesson_service = LessonService(db)
    lesson = lesson_service.get_by_id(lesson_id)
    
    if not lesson:
        raise HTTPException(
            status_code=404,
            detail="Lesson not found"
        )
    
    # Check if user is enrolled in the course
    if not lesson_service.is_user_enrolled(lesson.course_id, current_user.id):
        raise HTTPException(
            status_code=403,
            detail="You are not enrolled in this course"
        )
    
    lesson_service.update_lesson_progress(
        lesson_id, 
        current_user.id, 
        watch_time, 
        last_position
    )
    
    return {"message": "Progress updated successfully"}
