from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.schemas.course import (
    CourseResponse, 
    CourseCreate, 
    CourseUpdate, 
    CourseList,
    CourseDetailResponse
)
from app.services.course_service import CourseService
from app.api.deps import get_current_user, get_current_instructor, get_optional_current_user

router = APIRouter()


@router.get("/", response_model=CourseList)
def get_courses(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category_id: int = Query(None),
    level: str = Query(None),
    status: str = Query("published"),
    is_featured: bool = Query(None),
    is_bestseller: bool = Query(None),
    search: str = Query(None),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_optional_current_user)
) -> Any:
    """Get list of courses with filters"""
    course_service = CourseService(db)
    
    courses = course_service.get_courses(
        skip=skip,
        limit=limit,
        category_id=category_id,
        level=level,
        status=status,
        is_featured=is_featured,
        is_bestseller=is_bestseller,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    total = course_service.count_courses(
        category_id=category_id,
        level=level,
        status=status,
        is_featured=is_featured,
        is_bestseller=is_bestseller,
        search=search
    )
    
    pages = (total + limit - 1) // limit
    
    return CourseList(
        courses=courses,
        total=total,
        page=(skip // limit) + 1,
        size=limit,
        pages=pages
    )


@router.get("/featured", response_model=List[CourseResponse])
def get_featured_courses(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
) -> Any:
    """Get featured courses"""
    course_service = CourseService(db)
    courses = course_service.get_featured_courses(limit=limit)
    return courses


@router.get("/bestsellers", response_model=List[CourseResponse])
def get_bestseller_courses(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
) -> Any:
    """Get bestseller courses"""
    course_service = CourseService(db)
    courses = course_service.get_bestseller_courses(limit=limit)
    return courses


@router.get("/my-courses", response_model=List[CourseResponse])
def get_my_courses(
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Get courses created by current instructor"""
    course_service = CourseService(db)
    courses = course_service.get_courses_by_instructor(current_user.id)
    return courses


@router.post("/", response_model=CourseResponse)
def create_course(
    course_data: CourseCreate,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Create new course (instructor only)"""
    course_service = CourseService(db)
    
    # Set instructor_id to current user
    course_data.instructor_id = current_user.id
    
    course = course_service.create_course(course_data)
    return course


@router.get("/{course_id}", response_model=CourseDetailResponse)
def get_course(
    course_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_optional_current_user)
) -> Any:
    """Get course details"""
    course_service = CourseService(db)
    course = course_service.get_course_by_id(course_id)
    
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    # Check if course is published or user is instructor/admin
    if course.status != "published":
        if not current_user or (
            current_user.id != course.instructor_id and 
            current_user.role != "admin"
        ):
            raise HTTPException(
                status_code=404,
                detail="Course not found"
            )
    
    return course


@router.get("/slug/{course_slug}", response_model=CourseDetailResponse)
def get_course_by_slug(
    course_slug: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_optional_current_user)
) -> Any:
    """Get course details by slug"""
    course_service = CourseService(db)
    course = course_service.get_course_by_slug(course_slug)
    
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    # Check if course is published or user is instructor/admin
    if course.status != "published":
        if not current_user or (
            current_user.id != course.instructor_id and 
            current_user.role != "admin"
        ):
            raise HTTPException(
                status_code=404,
                detail="Course not found"
            )
    
    return course


@router.put("/{course_id}", response_model=CourseResponse)
def update_course(
    course_id: int,
    course_update: CourseUpdate,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Update course (instructor/admin only)"""
    course_service = CourseService(db)
    course = course_service.get_course_by_id(course_id)
    
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    # Check permissions
    if current_user.role != "admin" and current_user.id != course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    updated_course = course_service.update_course(course, course_update)
    return updated_course


@router.delete("/{course_id}")
def delete_course(
    course_id: int,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Delete course (instructor/admin only)"""
    course_service = CourseService(db)
    course = course_service.get_course_by_id(course_id)
    
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    # Check permissions
    if current_user.role != "admin" and current_user.id != course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    course_service.delete_course(course)
    return {"message": "Course deleted successfully"}


@router.post("/{course_id}/publish")
def publish_course(
    course_id: int,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Publish course (instructor/admin only)"""
    course_service = CourseService(db)
    course = course_service.get_course_by_id(course_id)
    
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    # Check permissions
    if current_user.role != "admin" and current_user.id != course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    course_service.publish_course(course)
    return {"message": "Course published successfully"}


@router.post("/{course_id}/unpublish")
def unpublish_course(
    course_id: int,
    current_user: User = Depends(get_current_instructor),
    db: Session = Depends(get_db)
) -> Any:
    """Unpublish course (instructor/admin only)"""
    course_service = CourseService(db)
    course = course_service.get_course_by_id(course_id)
    
    if not course:
        raise HTTPException(
            status_code=404,
            detail="Course not found"
        )
    
    # Check permissions
    if current_user.role != "admin" and current_user.id != course.instructor_id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )
    
    course_service.unpublish_course(course)
    return {"message": "Course unpublished successfully"}
