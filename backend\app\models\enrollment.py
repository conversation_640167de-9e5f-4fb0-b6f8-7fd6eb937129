from sqlalchemy import Column, <PERSON>teger, String, Boolean, DateTime, DECIMAL, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Enrollment(Base):
    __tablename__ = "enrollments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    course_id = Column(Integer, ForeignKey("courses.id"))
    enrolled_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    progress_percentage = Column(DECIMAL(5, 2), default=0)
    last_accessed_at = Column(DateTime(timezone=True))
    certificate_issued_at = Column(DateTime(timezone=True))
    certificate_url = Column(String(500))

    # Unique constraint to prevent duplicate enrollments
    __table_args__ = (UniqueConstraint('user_id', 'course_id', name='unique_user_course_enrollment'),)

    # Relationships
    user = relationship("User", back_populates="enrollments")
    course = relationship("Course", back_populates="enrollments")
    lesson_progress = relationship("LessonProgress", back_populates="enrollment")

    def __repr__(self):
        return f"<Enrollment(id={self.id}, user_id={self.user_id}, course_id={self.course_id})>"


class LessonProgress(Base):
    __tablename__ = "lesson_progress"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    lesson_id = Column(Integer, ForeignKey("lessons.id"))
    enrollment_id = Column(Integer, ForeignKey("enrollments.id"))
    is_completed = Column(Boolean, default=False)
    watch_time = Column(Integer, default=0)  # in seconds
    completed_at = Column(DateTime(timezone=True))
    last_position = Column(Integer, default=0)  # video position in seconds
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint to prevent duplicate progress records
    __table_args__ = (UniqueConstraint('user_id', 'lesson_id', name='unique_user_lesson_progress'),)

    # Relationships
    user = relationship("User", back_populates="lesson_progress")
    lesson = relationship("Lesson", back_populates="lesson_progress")
    enrollment = relationship("Enrollment", back_populates="lesson_progress")

    def __repr__(self):
        return f"<LessonProgress(id={self.id}, user_id={self.user_id}, lesson_id={self.lesson_id}, completed={self.is_completed})>"


class Review(Base):
    __tablename__ = "reviews"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    course_id = Column(Integer, ForeignKey("courses.id"))
    rating = Column(Integer)  # 1-5 stars
    title = Column(String(255))
    comment = Column(String(1000))
    is_published = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint to prevent multiple reviews from same user for same course
    __table_args__ = (UniqueConstraint('user_id', 'course_id', name='unique_user_course_review'),)

    # Relationships
    user = relationship("User", back_populates="reviews")
    course = relationship("Course", back_populates="reviews")

    def __repr__(self):
        return f"<Review(id={self.id}, user_id={self.user_id}, course_id={self.course_id}, rating={self.rating})>"
