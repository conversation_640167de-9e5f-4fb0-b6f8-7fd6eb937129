version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: eduplatform_postgres
    environment:
      POSTGRES_DB: eduplatform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - eduplatform_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: eduplatform_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - eduplatform_network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: eduplatform_backend
    environment:
      - DATABASE_URL=***********************************************/eduplatform
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-here
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - eduplatform_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: eduplatform_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - eduplatform_network
    command: npm start

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: eduplatform_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - eduplatform_network

volumes:
  postgres_data:
  redis_data:

networks:
  eduplatform_network:
    driver: bridge
