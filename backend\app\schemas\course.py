from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from uuid import UUID


class CategoryBase(BaseModel):
    name: str
    slug: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    image_url: Optional[str] = None
    icon: Optional[str] = None
    sort_order: Optional[int] = 0


class CategoryCreate(CategoryBase):
    pass


class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    icon: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None


class CategoryResponse(CategoryBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    children: Optional[List['CategoryResponse']] = []
    course_count: Optional[int] = 0

    class Config:
        from_attributes = True


class CourseBase(BaseModel):
    title: str
    slug: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    thumbnail_url: Optional[str] = None
    preview_video_url: Optional[str] = None
    price: Optional[Decimal] = 0
    original_price: Optional[Decimal] = None
    level: Optional[str] = "beginner"
    language: Optional[str] = "vi"
    category_id: Optional[int] = None
    requirements: Optional[str] = None
    what_you_learn: Optional[str] = None
    target_audience: Optional[str] = None


class CourseCreate(CourseBase):
    instructor_id: int


class CourseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    short_description: Optional[str] = None
    thumbnail_url: Optional[str] = None
    preview_video_url: Optional[str] = None
    price: Optional[Decimal] = None
    original_price: Optional[Decimal] = None
    level: Optional[str] = None
    category_id: Optional[int] = None
    status: Optional[str] = None
    is_featured: Optional[bool] = None
    is_bestseller: Optional[bool] = None
    requirements: Optional[str] = None
    what_you_learn: Optional[str] = None
    target_audience: Optional[str] = None


class InstructorInfo(BaseModel):
    id: int
    full_name: str
    avatar_url: Optional[str] = None
    bio: Optional[str] = None

    class Config:
        from_attributes = True


class CourseResponse(CourseBase):
    id: int
    uuid: UUID
    duration_hours: int
    total_lessons: int
    status: str
    is_featured: bool
    is_bestseller: bool
    certificate_available: bool
    avg_rating: Decimal
    total_reviews: int
    total_students: int
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None
    instructor: Optional[InstructorInfo] = None
    category: Optional[CategoryResponse] = None

    class Config:
        from_attributes = True


class CourseList(BaseModel):
    courses: List[CourseResponse]
    total: int
    page: int
    size: int
    pages: int


class CourseSectionBase(BaseModel):
    title: str
    description: Optional[str] = None
    sort_order: Optional[int] = 0


class CourseSectionCreate(CourseSectionBase):
    course_id: int


class CourseSectionUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None


class CourseSectionResponse(CourseSectionBase):
    id: int
    course_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LessonBase(BaseModel):
    title: str
    slug: str
    description: Optional[str] = None
    content: Optional[str] = None
    video_url: Optional[str] = None
    video_duration: Optional[int] = 0
    lesson_type: Optional[str] = "video"
    sort_order: Optional[int] = 0
    is_preview: Optional[bool] = False


class LessonCreate(LessonBase):
    course_id: int
    section_id: int


class LessonUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    lesson_type: Optional[str] = None
    sort_order: Optional[int] = None
    is_preview: Optional[bool] = None
    is_published: Optional[bool] = None


class LessonResponse(LessonBase):
    id: int
    uuid: UUID
    course_id: int
    section_id: int
    is_published: bool
    resources: Optional[dict] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CourseDetailResponse(CourseResponse):
    sections: List[CourseSectionResponse] = []
    lessons: List[LessonResponse] = []
